<template>
  <div class="company-knowledge-container">
    <div class="memo-title">公司知识库</div>
    <div class="memo-content">
      <!-- 内容区域暂时留空 -->
    </div>
    <div class="memo-add-btn" @click="handleAddClick">
      <span class="add-icon">+</span>
    </div>
  </div>
</template>

<script setup lang="ts">
// 处理添加按钮点击事件
const handleAddClick = () => {
  console.log('公司知识库添加按钮被点击');
};
</script>

<style lang="scss" scoped>
.company-knowledge-container {
  height: 80px;
  width: 100%;
  background: #e8f5e8; // 浅绿色底
  border-radius: 12px;
  display: flex;
  align-items: center;
  padding: 16px;
  box-sizing: border-box;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .memo-title {
    color: #4caf50; // 绿色标题
    font-size: 26px;
    font-weight: 600;
    white-space: nowrap;
    margin-right: 16px;
  }

  .memo-content {
    flex: 1;
    height: 100%;
    // 内容区域暂时留空
  }

  .memo-add-btn {
    width: 40px;
    height: 40px;
    background: #4caf50;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: #388e3c;
      transform: scale(1.05);
    }

    .add-icon {
      color: white;
      font-size: 24px;
      font-weight: bold;
    }
  }
}
</style>
